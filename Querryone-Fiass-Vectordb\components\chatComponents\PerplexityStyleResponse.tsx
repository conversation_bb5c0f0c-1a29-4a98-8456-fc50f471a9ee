import React, { useState, useRef, useEffect } from 'react';
import WebpagePreview from './WebpagePreview';
import { Pi<PERSON>aretRight, <PERSON><PERSON><PERSON>bulb, <PERSON>Link, PiX } from 'react-icons/pi';
import { motion, AnimatePresence } from 'framer-motion';
import ClientOnly from '@/components/ui/ClientOnly';

type Language = 'english' | 'tamil' | 'telugu' | 'kannada';

interface PerplexityStyleResponseProps {
  text: string;
  sentenceAnalysis?: Array<{
    sentence: string;
    url: string;
    summary?: string;
  }>;
  relatedQuestions?: string[];
  onSelectQuestion?: (question: string) => void;
  selectedLanguage?: string;
}

// Interface for the preview data
interface PreviewData {
  url: string;
  summary?: string;
  referenceNumber: number;
  position: {
    x: number;
    y: number;
  } | null;
}

const PerplexityStyleResponse: React.FC<PerplexityStyleResponseProps> = ({
  text,
  sentenceAnalysis,
  relatedQuestions,
  onSelectQuestion,
  selectedLanguage = "English"
}) => {
  // Debug logging for related questions
  console.log("🔍 PerplexityStyleResponse - Related Questions Debug:");
  console.log("📊 relatedQuestions:", relatedQuestions);
  console.log("📊 relatedQuestions length:", relatedQuestions?.length);
  console.log("📊 relatedQuestions type:", typeof relatedQuestions);
  if (relatedQuestions) {
    relatedQuestions.forEach((q, i) => console.log(`📊 Question ${i + 1}:`, q));
  }
  const [activeReference, setActiveReference] = useState<number | null>(null);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const referenceRefs = useRef<Record<number, HTMLButtonElement | null>>({});
  const referenceSpanRefs = useRef<Record<number, HTMLSpanElement | null>>({});
  const previewTimerRef = useRef<NodeJS.Timeout | null>(null);
  const hideTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isHoveringPreviewRef = useRef<boolean>(false);

  // Function to detect language from text content
  const detectLanguageFromText = (text: string): Language => {
    // Tamil Unicode range: \u0B80-\u0BFF
    const tamilRegex = /[\u0B80-\u0BFF]/;
    // Telugu Unicode range: \u0C00-\u0C7F
    const teluguRegex = /[\u0C00-\u0C7F]/;
    // Kannada Unicode range: \u0C80-\u0CFF
    const kannadaRegex = /[\u0C80-\u0CFF]/;

    if (tamilRegex.test(text)) {
      return 'tamil';
    } else if (teluguRegex.test(text)) {
      return 'telugu';
    } else if (kannadaRegex.test(text)) {
      return 'kannada';
    } else {
      return 'english';
    }
  };

  // Handle reference link navigation based on detected language from response
  const handleReferenceClick = (url: string, referenceNumber?: number) => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Find the corresponding sentence analysis item to get source type
    const sentenceItem = sentenceAnalysis?.find((item, index) => (index + 1) === referenceNumber);
    const sourceType = (sentenceItem as any)?.source_type;

    // Detect language from the actual response text instead of selectedLanguage prop
    const detectedLanguage = detectLanguageFromText(text);

    // Debug logging
    console.log('Reference click - selectedLanguage prop:', selectedLanguage);
    console.log('Reference click - detected language from text:', detectedLanguage);
    console.log('Reference click - original URL:', url);
    console.log('Reference click - source type:', sourceType);
    console.log('Reference click - sample text for detection:', text.substring(0, 100));

    // Handle file:// URLs (local documents/PDFs/audio files)
    if (url.startsWith('file://')) {
      console.log('Local file detected, cannot open directly in browser');
      // For local files, we could show a message or handle differently
      alert(`This reference points to a local ${sourceType || 'file'}: ${(sentenceItem as any)?.source_title || 'Unknown'}`);
      return;
    }

    // Handle "Not found" or invalid URLs
    if (url === 'Not found' || url === 'N/A') {
      console.log('Invalid URL detected');
      alert('This reference does not have a valid URL to open.');
      return;
    }

    if (detectedLanguage === 'english') {
      // For English, open external URL in new tab
      console.log('Opening external URL in new tab');
      window.open(url, '_blank', 'noopener,noreferrer');
    } else {
      // For non-English languages, open language-specific page in NEW TAB with URL parameters
      try {
        const urlObj = new URL(url);
        const domain = urlObj.hostname;

        // Create query parameters for the language page
        const params = new URLSearchParams({
          url: url,
          domain: domain,
          referenceNumber: (referenceNumber || 1).toString(),
          returnUrl: window.location.pathname + window.location.search,
          sourceType: sourceType || 'unknown'
        });

        const languagePage = `/${detectedLanguage}?${params.toString()}`;

        // Open language page in NEW TAB instead of same tab
        console.log('Opening language page in new tab with params:', languagePage);
        window.open(languagePage, '_blank', 'noopener,noreferrer');
      } catch (error) {
        console.error('Error parsing URL for navigation:', error);
        // Fallback to simple navigation in new tab
        const languagePage = `/${detectedLanguage}`;
        window.open(languagePage, '_blank', 'noopener,noreferrer');
      }
    }
  };

  // Handle question selection
  const handleQuestionSelect = (question: string) => {
    console.log("🔄 PerplexityStyleResponse: handleQuestionSelect called with:", question);
    
    if (onSelectQuestion) {
      // Remove markdown formatting from the question
      const cleanQuestion = question.replace(/\*\*/g, '');
      console.log("✅ PerplexityStyleResponse: Calling onSelectQuestion with:", cleanQuestion);
      
      // Call onSelectQuestion with the cleaned question
      onSelectQuestion(cleanQuestion);
      
      // Also try to update the input directly as a fallback
      const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent') as HTMLInputElement;
      if (inputElement) {
        inputElement.value = cleanQuestion;
        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
        inputElement.focus();
      }
    } else {
      console.warn("❌ PerplexityStyleResponse: onSelectQuestion prop is not provided!");
    }
  };

  // Ref callback that properly handles the TypeScript typing
  const setReferenceRef = (referenceNumber: number) => (el: HTMLButtonElement | null) => {
    referenceRefs.current[referenceNumber] = el;
  };

  // Add logging for debugging
  useEffect(() => {
    // Log the props for debugging
    console.log("PerplexityStyleResponse props:", { text, sentenceAnalysis, relatedQuestions, selectedLanguage });

    // Explicitly log related questions
    if (relatedQuestions && relatedQuestions.length > 0) {
      console.log("PerplexityStyleResponse has related questions:", relatedQuestions);
    } else {
      console.warn("PerplexityStyleResponse has no related questions");
    }

    // Log language information
    console.log("PerplexityStyleResponse selectedLanguage:", selectedLanguage);
    console.log("PerplexityStyleResponse detected language from text:", detectLanguageFromText(text));
  }, [text, sentenceAnalysis, relatedQuestions, selectedLanguage]);

  // Ref callback for reference number spans
  const setReferenceSpanRef = (referenceNumber: number) => (el: HTMLSpanElement | null) => {
    referenceSpanRefs.current[referenceNumber] = el;
  };

  // Handle mouse enter on reference number with improved debounce
  const handleReferenceMouseEnter = (referenceNumber: number, url: string, summary?: string) => {
    // Clear any existing hide timer
    if (hideTimerRef.current) {
      clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }

    // Set active reference immediately for visual feedback
    setActiveReference(referenceNumber);

    // Clear any existing preview timer
    if (previewTimerRef.current) {
      clearTimeout(previewTimerRef.current);
    }

    // Use a small timeout to prevent flickering when moving mouse quickly
    previewTimerRef.current = setTimeout(() => {
      // Get position of the reference span
      const spanElement = referenceSpanRefs.current[referenceNumber];
      if (spanElement) {
        const rect = spanElement.getBoundingClientRect();
        setPreviewData({
          url,
          summary,
          referenceNumber,
          position: {
            x: rect.left,
            y: rect.top
          }
        });
      }
      previewTimerRef.current = null;
    }, 100); // Reduced delay for better responsiveness
  };

  // Handle mouse leave with improved stability
  const handleReferenceMouseLeave = () => {
    // Don't hide immediately, wait to see if user is moving to another reference
    // or to the preview itself
    hideTimerRef.current = setTimeout(() => {
      if (!isHoveringPreviewRef.current) {
        setActiveReference(null);
        setPreviewData(null);
      }
      hideTimerRef.current = null;
    }, 200);
  };

  // Removed the auto-scrolling effect when hovering references
  // This prevents the page from jumping around when hovering references

  // Cleanup effect to clear any timers when component unmounts
  useEffect(() => {
    return () => {
      if (previewTimerRef.current) {
        clearTimeout(previewTimerRef.current);
      }
      if (hideTimerRef.current) {
        clearTimeout(hideTimerRef.current);
      }
    };
  }, []);

  // Function to process markdown formatting
  const processMarkdown = (content: string, keyPrefix: string = 'md'): React.ReactNode => {
    if (!content) return content;

    // Process the content in stages to handle different markdown elements

    // Stage 1: Handle bold text (text between ** **)
    const processBold = (text: string): React.ReactNode[] => {
      const boldRegex = /\*\*(.*?)\*\*/g;
      const parts = text.split(boldRegex);

      if (parts.length === 1) {
        return [text]; // No bold formatting found
      }

      const result: React.ReactNode[] = [];

      // Process the parts - odd indices are the bold text
      parts.forEach((part, index) => {
        if (index % 2 === 0) {
          // Regular text
          if (part) result.push(part);
        } else {
          // Bold text
          result.push(<strong key={`bold-${index}`}>{part}</strong>);
        }
      });

      return result;
    };

    // Stage 2: Handle italic text (text between * *)
    const processItalic = (nodes: React.ReactNode[]): React.ReactNode[] => {
      const result: React.ReactNode[] = [];

      nodes.forEach((node, nodeIndex) => {
        if (typeof node !== 'string') {
          // If it's already a React element, keep it as is
          result.push(node);
          return;
        }

        const text = node as string;
        const italicRegex = /\*(.*?)\*/g;
        const parts = text.split(italicRegex);

        if (parts.length === 1) {
          result.push(text); // No italic formatting found
          return;
        }

        // Process the parts - odd indices are the italic text
        parts.forEach((part, index) => {
          if (index % 2 === 0) {
            // Regular text
            if (part) result.push(part);
          } else {
            // Italic text
            result.push(<em key={`italic-${nodeIndex}-${index}`}>{part}</em>);
          }
        });
      });

      return result;
    };

    // Stage 3: Handle headers (lines starting with # or ## or ###)
    const processHeaders = (text: string, keyPrefix: string = 'header'): React.ReactNode => {
      if (!text) return text;

      const trimmedText = text.trim();

      // Check if the text starts with header markers
      if (trimmedText.startsWith('### ')) {
        return <h3 key={`${keyPrefix}-h3`} className="text-lg font-bold mt-5 mb-3 text-gray-800 dark:text-gray-200">{trimmedText.substring(4)}</h3>;
      } else if (trimmedText.startsWith('## ')) {
        return <h2 key={`${keyPrefix}-h2`} className="text-xl font-bold mt-6 mb-3 text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-2">{trimmedText.substring(3)}</h2>;
      } else if (trimmedText.startsWith('# ')) {
        return <h1 key={`${keyPrefix}-h1`} className="text-2xl font-bold mt-6 mb-4 text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">{trimmedText.substring(2)}</h1>;
      }

      // Check if it's a list item
      if (trimmedText.startsWith('- ')) {
        return <li key={`${keyPrefix}-li`} className="ml-5 mb-2 text-base leading-relaxed">{trimmedText.substring(2)}</li>;
      }

      // If it's not a header or list item, process other markdown elements
      const boldProcessed = processBold(text);
      const italicProcessed = processItalic(boldProcessed);

      // For regular text, return as a React fragment to prevent unwanted p nesting
      return <React.Fragment key={`${keyPrefix}-fragment`}>{italicProcessed}</React.Fragment>;
    };

    // Check if the content is a header or list item first
    // Handle headers and list items first
    const trimmedContent = content.trim();
    if (trimmedContent.startsWith('#') || trimmedContent.startsWith('- ')) {
      return processHeaders(content, keyPrefix);
    }

    // Otherwise process other markdown elements
    const boldProcessed = processBold(content);
    const italicProcessed = processItalic(boldProcessed);

    return italicProcessed;
  };

  // If there's no sentence analysis data, just return the text with markdown processing
  if (!sentenceAnalysis || !Array.isArray(sentenceAnalysis) || sentenceAnalysis.length === 0) {
    return <div className="space-y-4">{processMarkdown(text, 'main-content')}</div>;
  }

  // Create a map of sentences to their reference numbers
  const sentenceToReferenceMap = new Map<string, number>();
  sentenceAnalysis.forEach((item, index) => {
    sentenceToReferenceMap.set(item.sentence, index + 1);
  });

  // Split the text into paragraphs
  const paragraphs = text.split('\n').filter(p => p.trim() !== '');

  // Check if we have a list of items
  const isList = paragraphs.some(p => p.trim().startsWith('- '));

  // Process each paragraph to add reference numbers
  const processedParagraphs = paragraphs.map((paragraph, paragraphIndex) => {
    // Check if this paragraph is a header or list item
    const trimmedParagraph = paragraph.trim();
    if (trimmedParagraph.startsWith('#') || trimmedParagraph.startsWith('- ')) {
      // Find all sentences in this paragraph that have references
      const matchingSentences = sentenceAnalysis.filter(item =>
        paragraph.includes(item.sentence)
      );

      if (matchingSentences.length === 0) {
        // If no matching sentences, process the markdown formatting
        return processMarkdown(paragraph, `para-${paragraphIndex}`);
      }
    }

    // Find all sentences in this paragraph that have references
    const matchingSentences = sentenceAnalysis.filter(item =>
      paragraph.includes(item.sentence)
    );

    if (matchingSentences.length === 0) {
      // If no matching sentences, return the paragraph with markdown processing
      const processed = processMarkdown(paragraph, `para-content-${paragraphIndex}`);

      // Check if the processed content is a header element
      if (React.isValidElement(processed) &&
          typeof processed.type === 'string' &&
          ['h1','h2','h3'].includes(processed.type)) {
        // Return header elements directly without wrapping in <p>
        return processed;
      } else {
        // For non-header content, check if it contains header elements
        const paragraphText = paragraph.trim();
        if (paragraphText.startsWith('### ') || paragraphText.startsWith('## ') || paragraphText.startsWith('# ')) {
          // If the paragraph starts with header markdown, process it as a header
          return processMarkdown(paragraph, `para-content-${paragraphIndex}`);
        } else {
          // Safe to wrap in <p> tag
          return <p key={`paragraph-${paragraphIndex}`}>{processed}</p>;
        }
      }
    }

    // Sort matching sentences by their position in the paragraph
    matchingSentences.sort((a, b) => {
      return paragraph.indexOf(a.sentence) - paragraph.indexOf(b.sentence);
    });

    // Process the paragraph to add reference numbers
    let lastIndex = 0;
    const parts: React.ReactNode[] = [];

    matchingSentences.forEach((item, index) => {
      const sentenceIndex = paragraph.indexOf(item.sentence, lastIndex);

      if (sentenceIndex === -1) return; // Skip if sentence not found

      // Add text before the sentence with markdown processing
      if (sentenceIndex > lastIndex) {
        parts.push(
          <span key={`before-${paragraphIndex}-${index}`}>
            {processMarkdown(paragraph.substring(lastIndex, sentenceIndex), `before-md-${paragraphIndex}-${index}`)}
          </span>
        );
      }

      // Get the reference number for this sentence
      const referenceNumber = sentenceToReferenceMap.get(item.sentence);

      // Add the sentence with the reference number and markdown processing
      parts.push(
        <span key={`sentence-${paragraphIndex}-${index}`}>
          {processMarkdown(item.sentence, `sentence-md-${paragraphIndex}-${index}`)}
          <span
            ref={setReferenceSpanRef(referenceNumber || 0)}
            className="reference-number-hover inline-flex items-center justify-center ml-0.5 text-[10px] font-medium text-primaryColor bg-primaryColor/10 rounded-full w-[16px] h-[16px] hover:bg-primaryColor/20 transition-all duration-200"
            onMouseEnter={() => handleReferenceMouseEnter(referenceNumber || 0, item.url, item.summary)}
            onMouseLeave={handleReferenceMouseLeave}
            // Removed onClick handler to prevent navigation
            style={{ verticalAlign: 'super' }}
          >
            {referenceNumber}
          </span>
        </span>
      );

      lastIndex = sentenceIndex + item.sentence.length;
    });

    // Add any remaining text after the last matched sentence with markdown processing
    if (lastIndex < paragraph.length) {
      parts.push(
        <span key={`after-${paragraphIndex}`}>
          {processMarkdown(paragraph.substring(lastIndex), `after-md-${paragraphIndex}`)}
        </span>
      );
    }

    // Check if any part contains header elements or if the paragraph starts with header markdown
    const hasHeader = parts.some(part =>
      React.isValidElement(part) &&
      typeof part.type === 'string' &&
      ['h1','h2','h3'].includes(part.type)
    );

    // Also check if the original paragraph text contains header markdown
    const paragraphText = paragraph.trim();
    const containsHeaderMarkdown = paragraphText.includes('### ') || paragraphText.includes('## ') || paragraphText.includes('# ');

    // If there are headers or header markdown, use a div container instead of p
    return (hasHeader || containsHeaderMarkdown) ? (
      <div key={`paragraph-${paragraphIndex}`} className="mb-4 leading-relaxed text-base">{parts}</div>
    ) : (
      <p key={`paragraph-${paragraphIndex}`} className="mb-4 leading-relaxed text-base">{parts}</p>
    );
  });

  // Check if we need to wrap list items in a ul element
  const wrappedProcessedParagraphs = isList ? (
    <ul className="list-disc list-outside pl-5 my-4 space-y-2">
      {processedParagraphs.map((paragraph, index) => (
        <React.Fragment key={`list-item-${index}`}>
          {paragraph}
        </React.Fragment>
      ))}
    </ul>
  ) : (
    processedParagraphs.map((paragraph, index) => (
      <React.Fragment key={`paragraph-wrapper-${index}`}>
        {paragraph}
      </React.Fragment>
    ))
  );

  const [showRelatedModal, setShowRelatedModal] = useState(false);


  // Helper function to check if there are any valid references (not N/A or Not found)
  const hasValidReferences = () => {
    if (!sentenceAnalysis || sentenceAnalysis.length === 0) {
      return false;
    }

    return sentenceAnalysis.some(item =>
      item.url &&
      item.url !== 'N/A' &&
      item.url !== 'Not found' &&
      item.url.trim() !== ''
    );
  };

  return (
    <div className="whitespace-pre-wrap relative">
      <div className="mb-6 leading-relaxed text-base">
        {wrappedProcessedParagraphs}
      </div>

      {/* Related Questions section */}
      {relatedQuestions && relatedQuestions.length > 0 && (
        <>
          <div className="mt-6 pt-4 border-t border-primaryColor/10">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-n700 dark:text-n30 flex items-center">
                <PiLightbulb className="h-4 w-4 mr-2 text-primaryColor" />
                Related Questions
              </h3>
              <button
                onClick={() => setShowRelatedModal(true)}
                className="text-xs text-primaryColor hover:text-primaryColor/80 transition-colors duration-200 flex items-center"
              >
                View All
              </button>
            </div>
            <div className="flex flex-col gap-3">
              {relatedQuestions.slice(0, 3).map((question, index) => (
                <motion.div
                  key={`related-question-${index}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="border border-primaryColor/10 rounded-lg p-3 hover:bg-primaryColor/5 transition-colors duration-200"
                >
                  <div
                    className="flex justify-between items-center cursor-pointer"
                    onClick={() => {
                      // Always select the question, no toggling
                      handleQuestionSelect(question);
                    }}
                  >
                    <div className="text-sm text-n700 dark:text-n30 flex-1 font-medium">
                      {/* Remove markdown formatting from the question */}
                      {question.replace(/\*\*/g, '')}
                    </div>
                    <div className="text-white p-1.5 rounded-md bg-gradient-to-r from-primaryColor to-blue-600 shadow-sm">
                      <PiCaretRight />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Modal for Related Questions */}
          <AnimatePresence>
            {showRelatedModal && (
              <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.2 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col"
                >
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <h3 className="text-lg font-medium text-n700 dark:text-n30">Related Questions</h3>
                    <button
                      onClick={() => setShowRelatedModal(false)}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      <PiX className="h-5 w-5" />
                    </button>
                  </div>
                  <div className="p-4 overflow-y-auto flex-1">
                    <div className="grid gap-3">
                      {relatedQuestions.map((question, index) => (
                        <motion.div
                          key={`modal-question-${index}`}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.2, delay: index * 0.05 }}
                          className="border border-primaryColor/10 rounded-lg p-3 hover:bg-primaryColor/5 transition-colors duration-200"
                        >
                          <div
                            className="flex justify-between items-center cursor-pointer"
                            onClick={() => {
                              handleQuestionSelect(question);
                              setShowRelatedModal(false);
                            }}
                          >
                            <div className="text-sm text-n700 dark:text-n30 flex-1">
                              {question.replace(/\*\*/g, '')}
                            </div>
                            <div className="bg-gradient-to-r from-primaryColor to-blue-600 text-white p-1.5 rounded-md shadow-sm">
                              <PiCaretRight />
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </div>
            )}
          </AnimatePresence>
        </>
      )}

      {/* References section at the bottom - more compact design */}
      {sentenceAnalysis && sentenceAnalysis.length > 0 && hasValidReferences() && (
        <div className="mt-4 pt-3 border-t border-primaryColor/10">
          <div className="flex items-center mb-2">
            <PiLink className="h-3.5 w-3.5 mr-1.5 text-primaryColor" />
            <h3 className="text-xs font-medium text-n700 dark:text-n30">References</h3>
            <span className="ml-2 text-xs text-n400 dark:text-n200">
              {sentenceAnalysis.length} source{sentenceAnalysis.length !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="flex flex-wrap gap-1.5">
                {sentenceAnalysis.map((item, index) => {
                  const referenceNumber = index + 1;
                  // Extract domain name for display
                  let domain = '';
                  try {
                    const url = new URL(item.url);
                    domain = url.hostname.replace('www.', '');

                    // Clean up common domain patterns
                    if (domain.includes('youtube.com') || domain.includes('youtu.be')) {
                      domain = 'YouTube';
                    } else if (domain.includes('economictimes.indiatimes.com')) {
                      domain = 'Economic Times';
                    } else if (domain.includes('moneycontrol.com')) {
                      domain = 'MoneyControl';
                    } else if (domain.includes('financialexpress.com')) {
                      domain = 'Financial Express';
                    } else {
                      // Capitalize first letter and remove common TLDs for display
                      domain = domain.split('.')[0];
                      domain = domain.charAt(0).toUpperCase() + domain.slice(1);
                    }
                  } catch (error) {
                    // If URL parsing fails, try to extract a readable name
                    domain = item.url.length > 20 ? item.url.substring(0, 20) + '...' : item.url;
                  }

                  return (
                    <motion.button
                      key={`reference-compact-${index}`}
                      ref={setReferenceRef(referenceNumber)}
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.2, delay: index * 0.03 }}
                      className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-[10px] font-medium transition-all duration-200 ${
                        activeReference === referenceNumber
                          ? 'bg-primaryColor text-white'
                          : 'bg-primaryColor/10 text-primaryColor hover:bg-primaryColor/20'
                      }`}
                      onMouseEnter={() => handleReferenceMouseEnter(referenceNumber, item.url, item.summary)}
                      onMouseLeave={handleReferenceMouseLeave}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleReferenceClick(item.url, referenceNumber);
                      }}
                      title={item.url}
                    >
                      <span className="font-bold text-[9px]">{referenceNumber}</span>
                      <span className="max-w-[70px] truncate">{domain}</span>
                    </motion.button>
                  );
                })}
          </div>


          {/* Detailed reference information - shown on hover via the preview */}
        </div>
      )}

      {/* Floating preview - positioned absolutely to avoid nesting issues */}
      <AnimatePresence>
        {previewData && previewData.position && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 10 }}
            transition={{ duration: 0.2 }}
            className="fixed z-50 preview-container shadow-xl"
            style={{
              left: `${previewData.position.x}px`,
              top: previewData.position.y < 200 ? `${previewData.position.y + 20}px` : `${previewData.position.y - 200}px`
            }}
            // Add mouse enter/leave handlers to the preview container to prevent it from disappearing too quickly
            onMouseEnter={() => {
              // Track that we're hovering over the preview
              isHoveringPreviewRef.current = true;

              // Clear any hide timer
              if (hideTimerRef.current) {
                clearTimeout(hideTimerRef.current);
                hideTimerRef.current = null;
              }

              // Keep the reference active
              setActiveReference(previewData.referenceNumber);
            }}
            onMouseLeave={() => {
              // Track that we're no longer hovering over the preview
              isHoveringPreviewRef.current = false;

              // Use a delay before hiding the preview
              hideTimerRef.current = setTimeout(() => {
                setActiveReference(null);
                setPreviewData(null);
                hideTimerRef.current = null;
              }, 300);
            }}
          >
            <WebpagePreview
              url={previewData.url}
              summary={previewData.summary}
              referenceNumber={previewData.referenceNumber}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PerplexityStyleResponse;
